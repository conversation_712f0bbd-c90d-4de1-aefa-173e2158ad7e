namespace PuttPutt.Core.Interfaces;

internal interface IPlayerManagement
{
    // This interface is currently empty, but it can be expanded in the future
    // to include methods and properties related to player management.
    // For example, methods for adding, removing, or updating player information,
    // or properties for accessing player statistics.

    AddUpdatePlayerResult AddUpdatePlayer(string name, string color, bool isFavorite);
    bool RemovePlayer(string name);


}
