using PuttPutt.BDD.StepDefinitions.Shared;
using Reqnroll;

namespace PuttPutt.BDD.StepDefinitions.PlayerManagement;

[Binding]
public class PlayerManagementStepDefinitions
{
    private readonly ScenarioContext scenarioContext;
    private bool _isAppLaunched = false;
    private bool _isOnMainMenu = false;
    private bool _isOnPlayerManagementScreen = false;

    public PlayerManagementStepDefinitions(ScenarioContext scenarioContext)
    {
        this.scenarioContext = scenarioContext;
    }

    [Given("the app is launched")]
    public void GivenTheAppIsLaunched()
    {
        _isAppLaunched = true;
    }

    [Given("I am on the main menu")]
    public void GivenIAmOnTheMainMenu()
    {
        _isOnMainMenu = true;
        _isOnPlayerManagementScreen = false;
    }

    [Given("I navigate to the player management screen")]
    public void GivenINavigateToThePlayerManagementScreen()
    {
        //var navigateService = scenarioContext.GetService<INavigationService>();
        //navigateService.NavigateToPlayerManagement();

        _isOnPlayerManagementScreen = true;
        _isOnMainMenu = false; // Assuming navigating to player management screen takes you away from main menu
    }

    [Given("I have a player {string} with a red color")]
    public void GivenIHaveAPlayerWithARedColor(string playerName)
    {
        var player = scenarioContext.CreateOrGetTestPlayer(playerName, "Red");
        player.Color = "Red"; // Ensure color is set correctly even if player already existed
    }

    [Given("I have a player {string} in the player list")]
    public void GivenIHaveAPlayerInThePlayerList(string playerName)
    {
        scenarioContext.CreateOrGetTestPlayer(playerName);
    }

    [When("I tap the {string} button")]
    public void WhenITapTheButton(string p0)
    {
        switch (p0)
        {
            case "Add Player":
                if (!_isAppLaunched || !_isOnMainMenu)
                {
                    throw new InvalidOperationException("Cannot add player without launching the app and being on the main menu.");
                }
                // Logic to navigate to player management screen
                break;
            default:
                throw new PendingStepException($"Button '{p0}' is not implemented in this step definition.");
        }
    }

    [When("I enter {string} as the player name")]
    public void WhenIEnterAsThePlayerName(string p0)
    {
        throw new PendingStepException();
    }

    [When("I select a blue color for the player")]
    public void WhenISelectABlueColorForThePlayer()
    {
        throw new PendingStepException();
    }

    [When("I tap {string}")]
    public void WhenITap(string p0)
    {
        throw new PendingStepException();
    }

    [Then("the player {string} should be added to the player list")]
    public void ThenThePlayerShouldBeAddedToThePlayerList(string p0)
    {
        throw new PendingStepException();
    }

    [Then("the player should have a blue color indicator")]
    public void ThenThePlayerShouldHaveABlueColorIndicator()
    {
        throw new PendingStepException();
    }

    // Example step definitions showing how to use shared test data in assertions

    [Then("I should see all players from my player list")]
    public void ThenIShouldSeeAllPlayersFromMyPlayerList()
    {
        // Access shared test data for assertions
        var testPlayers = scenarioContext.GetTestPlayers();

        // In a real implementation, you would verify that the UI shows these players
        // For now, we'll just validate that we have the expected test data
        Assert.True(testPlayers.Count > 0, "Expected to have test players available");

        Console.WriteLine($"Verifying {testPlayers.Count} players are displayed:");
        foreach (var player in testPlayers)
        {
            Console.WriteLine($"  - Checking player: {player}");
            // Here you would add actual UI verification logic
        }
    }

    [Then("favorite players should appear at the top of the list")]
    public void ThenFavoritePlayersShouldAppearAtTheTopOfTheList()
    {
        // Access shared test data to get favorite players
        var favoriteTestPlayers = scenarioContext.GetFavoriteTestPlayers();
        var allTestPlayers = scenarioContext.GetTestPlayers();

        Assert.True(favoriteTestPlayers.Count > 0, "Expected to have favorite players for this test");

        Console.WriteLine($"Verifying {favoriteTestPlayers.Count} favorite players appear first:");
        foreach (var favoritePlayer in favoriteTestPlayers)
        {
            Console.WriteLine($"  - Favorite player: {favoritePlayer}");
            // Here you would add actual UI verification logic to ensure favorites are at the top
        }
    }

    [Then("the player {string} should be marked as a favorite player")]
    public void ThenThePlayerShouldBeMarkedAsAFavoritePlayer(string playerName)
    {
        // Access specific player from shared test data
        var player = scenarioContext.GetTestPlayer(playerName);

        Assert.NotNull(player);
        Assert.True(player.IsFavorite, $"Expected player '{playerName}' to be marked as favorite");

        Console.WriteLine($"Verified that {playerName} is marked as favorite");
    }

    [Then("I should see a list of existing players")]
    public void ThenIShouldSeeAListOfExistingPlayers()
    {
        // This step can use shared test data to verify expected players are shown
        var testPlayers = scenarioContext.GetTestPlayers();

        if (testPlayers.Count > 0)
        {
            Console.WriteLine($"Verifying display of {testPlayers.Count} existing players:");
            foreach (var player in testPlayers)
            {
                Console.WriteLine($"  - Expected to see: {player}");
                // Here you would add actual UI verification logic
            }
        }
        else
        {
            Console.WriteLine("No test players found - verifying empty state is handled correctly");
        }
    }
}